<?php

namespace App\Services;

use App\Helpers\HelperLibrary;
use App\Models\Carryout;
use App\Models\CarryoutProduct;
use App\Models\Location;
use App\Models\Pallet;
use App\Models\PalletProduct;
use App\Models\Process;
use App\Models\Product;
use App\Models\ProductLog;
use App\Models\RepairGrade;
use App\Models\RepairProcess;
use App\Models\RepairProduct;
use App\Models\RepairSymptom;
use App\Models\User;
use App\Models\WorkStatus;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class LoadedService
{
    // 기본 반환 데이터 설정
    private array $defaultData = [
        'isLocation' => false,
        'palletGradeCode' => '',
        'palletProdCount' => 0,
        'palletRecentProducts' => [],
    ];

    public function getLoadedLocations(): Collection|null
    {
        return Location::join('pallets', 'locations.id', '=', 'pallets.location_id')
            ->where('locations.enable', 'Y')
            ->whereIn('pallets.status', [Pallet::STATUS_REGISTERED, Pallet::STATUS_LOADED])
            ->where('locations.place', Location::LOCATION_COUNTRY_KR . '-' . Location::LOCATION_CITY_ESCS)
            ->where('locations.code', 'like', 'A-1-1-%')
            ->select('locations.code', 'locations.id')
            ->distinct()
            ->orderBy('locations.id', 'desc')
            ->get();
    }

    public function getLoadedPallet(Location $location): object|null
    {
        return Pallet::with(['palletProducts' => function ($query) {
            $query->where('status', PalletProduct::STATUS_REGISTERED);
        }, 'palletProducts.product'])
            ->where('location_id', $location->id)
            ->whereIn('status', [Pallet::STATUS_REGISTERED, Pallet::STATUS_LOADED])
            ->first();
    }

    public function addItem(array $items, string $palletNo, array $data = null): array
    {
        if ($data === null) {
            $data = $this->defaultData;
        }

        $items[] = array_merge(['pallet_no' => $palletNo], $data);

        return $items;
    }

    public function getLoadedPalletData($pallet): array
    {
        $palletProducts = $pallet->palletProducts;

        // 팔레트 제품이 없을 경우 기본 반환 값 반환 (isLocation만 true로 변경)
        if ($palletProducts === null) {
            return array_merge($this->defaultData, ['isLocation' => true]);
        }

        // 팔레트 제품이 있는 경우 데이터 설정
        $palletGradeCode = $palletProducts->first()->repairGrade->code;
        $palletProdCount = $palletProducts->count();
        $palletRecentProducts = $palletProducts->sortByDesc('id')
            ->take(10)
            ->map(fn($palletProduct) => $palletProduct->product->name)
            ->reverse()
            ->values()
            ->all();

        return [
            'isLocation' => true,
            'palletGradeCode' => $palletGradeCode ?? '',
            'palletProdCount' => $palletProdCount ?? 0,
            'palletRecentProducts' => $palletRecentProducts ?? []
        ];
    }

    public function makePalletCode($level, $column): string
    {
        if (empty($level) || empty($column)) {
            return '';
        }

        return $level . '-' . $column;
    }

    public function getSelectedPallet($data): array
    {
        $location = Location::where([
            'place' => $data['place'],
            'code' => $data['code'],
            'enable' => 'Y'
        ])->first();

        $isLocation = false;
        $palletGradeCode = '';
        $palletProdCount = 0;
        $palletRecentProducts = [];
        if ($location) {
            $pallet = $this->getLoadedPallet($location);

            if($pallet !== null){
                list('isLocation' => $isLocation,
                    'palletGradeCode' => $palletGradeCode,
                    'palletProdCount' => $palletProdCount,
                    'palletRecentProducts' => $palletRecentProducts
                ) = $this->getLoadedPalletData($pallet);
            }
        }

        return [
            'isLocation' => $isLocation,
            'palletGradeCode' => $palletGradeCode,
            'palletProdCount' => $palletProdCount,
            'palletRecentProducts' => $palletRecentProducts,
        ];
    }

    /**
     * 팔레트 적재시 적재 가능한 상품인지 확인
     * @param  string  $qaid
     * @return Product|null
     */
    public function getLoadedProductCheck(string $qaid): ?Product
    {
        return Product::distinct()
            ->select([
                'products.*',
                'reqs.req_at',
                DB::raw("(SELECT carryout_at FROM carryouts WHERE id = carryout_products.carryout_id AND status = ".Carryout::STATUS_CARRIED_IN.") as carryout_at"),
//                DB::raw("(SELECT code FROM repair_symptoms WHERE id = carryout_products.repair_symptom_id) as symptom_code"),
//                DB::raw("(SELECT code FROM repair_processes WHERE id = carryout_products.repair_process_id) as process_code"),
//                DB::raw("(SELECT code FROM repair_grades WHERE id = carryout_products.repair_grade_id) as grade_code"),
            ])
            ->without([ // $with에 정의된 관계들 중 이 쿼리에서 불필요한 것들을 제외
                'lot',
                'vendor',
                'link',
                'user',
                'checkedUser',
                'carryoutProducts',
            ])
            ->leftJoin('reqs', 'products.req_id', '=', 'reqs.id')
            ->leftJoin('carryout_products', function($join) {
                $join->on('products.id', '=', 'carryout_products.product_id')
                    ->where('carryout_products.status', '=', CarryoutProduct::STATUS_RENOVATED);
            })
            ->where('products.qaid', $qaid)
            ->whereNotIn('products.status', [Product::STATUS_HELD, Product::STATUS_DELETED])
            ->where('products.checked_status', Product::CHECKED_STATUS_CHECKED)
            ->orderByDesc('reqs.req_at')
            ->first();
    }

    /**
     * @todo: 사용 안 할듯...추후 이 부분이 필요 없다고 확신될 때 삭제할 것
     * 점검된 상품의 중복 상품이 있는지 확인 후 중복이 있다면 중복처리까지 완료하는 메서드
     * @throws Exception
     */
    public function handleCheckedProduct($palletProduct, $product, $qaid, $user): string
    {
        $pallet = $palletProduct->pallet->where('status', '!=', Pallet::STATUS_DELETED)->first();
        if ($pallet) {
            $message = "조회된 상품(QAID=" . $qaid . ")은 이미 점검완료한 상품입니다.";

            // 같은 qaid로 등록상태 다른 상품이 있는 경우엔 해당 상품 보류로 변경
            if ($product->status == Product::STATUS_CHECKED_ON_PALLET || $product->status == Product::STATUS_EXPORTED) {
                $duplicateProduct = Product::where('qaid', $qaid)
                    ->where('status', Product::STATUS_REGISTERED)
                    ->latest('id')
                    ->first();

                //등록상태의 같은 qaid상품이 조회되는 경우, 중복상품으로 표시하고 상품상태를 보류로 전환
                if ($duplicateProduct) {
                    // QAID 히스토리 로그 기록
                    $historyLogger = new HistoryLogger();
                    $historyLogger->setProduct($product)
                        ->setUser($user)
                        ->setTitle('QAID 중복 처리')
                        ->setPreviousStatus($product->status);

                    // 상품 테이블에 중복 표시
                    $duplicateProduct->update([
                        'duplicated' => 'Y',
                        'status' => Product::STATUS_HELD,
                    ]);

                    // 수집된 히스토리 데이터를 일괄 저장
                    try {
                        $historyLogger->setCurrentStatus($product->status)
                            ->setPalletId($pallet->id)
                            ->setPalletProductId($palletProduct->id)
                            ->save();
                    } catch (Exception $e) {
                        throw new Exception($e->getMessage());
                    }

                    // 카운터 기록
                    // 중복 상품일 경우 중복(duplicated): +1, 입고검수(대기)(unchecked): -1
                    $req_id = $duplicateProduct->req_id; # 카운팅을 하기 위해 req_id를 저장해 둔다
                    $counters[$req_id]['duplicated'] = 1;
                    $counters[$req_id]['unchecked'] = -1;

                    // 실제 카운터 기록
                    $countService = new CountService();
                    $countService->multipleUpdate($counters);
                }

                $message .= " 같은 QAID=" . $qaid . "로 중복된 상품(보류처리)이 있습니다.";
            }

            return $message;
        }

        return '';
    }

    public function isAlreadyChecked(Product $product): Model|null
    {
        return $product->palletProducts()
            ->where('status', '!=', PalletProduct::STATUS_REGISTERED)
            ->whereHas('pallet', function ($query) {
                $query->where('status', '!=', Pallet::STATUS_DELETED);
            })->first();
    }

    /**
     * @todo: 출고 팔레트에 적재중::저장 관련 프로세스를 수정해야 한다.
     * 기존에는 데이터를 저장했지만, 지금은 repairProduct의 상품 번호를 저장하는 방식으로 변경해야 한다.
     * @throws Exception|Throwable
     */
    public function storeProductOnPallet($data, User $user): void
    {
        try {
            DB::beginTransaction();

            // 팔레트 위치 가져 옴
            $locationService = new LocationService();
            $location = $locationService->store($data['location_place'], $data['location_code']);

            // 팔레트 가져 옴
            $palletService = new PalletService();
            $pallet = $palletService->store($location, $user);
            Log::channel('pallet')->info('팔레트 생성 여부: ', [
                'pallet' => $pallet
            ]);

            $repairProduct = RepairProduct::where('id', $data['repair_product_id'])
                ->without([
                    'repairSymptom',
                    'repairProcess',
                    'repairGrade',
                    'waitingUser',
                    'completedUser',
                    'repairProductParts',
                ])
                ->first();
            $product = $repairProduct->product
                ->without([
                    'req',
                    'cate4',
                    'cate5',
                    'lot',
                    'vendor',
                    'link',
                    'user',
                    'checkedUser',
                    'carryoutProducts',
                    'repairProduct',
                ]);

            // 상품 가져 옴
            if ($product === null) {
                throw new Exception("점검 가능한 상품(".$data['qaid'].")이 아닙니다.");
            }

            if ($product->palletProducts->isNotEmpty()) {
                throw new Exception("이미 점검 완료된 상품 [".$product->qaid.":".$product->name."] 입니다.");
            }

            // 각 프로세스 index 가져 옴
            $symptomId = $repairProduct->repair_symptom_id;
            $processId = $repairProduct->repair_process_id;
            $gradeId = $repairProduct->repair_grade_id;
            $invoice1 = $repairProduct->invoice1;
            $invoice2 = $repairProduct->invoice2;
            $invoice3 = $repairProduct->invoice3;

            $palletProduct = new PalletProduct();

            $palletProduct->pallet_id = $pallet->id;
            $palletProduct->product_id = $product->id;
            $palletProduct->repair_product_id = $repairProduct->id;
            $palletProduct->status = PalletProduct::STATUS_REGISTERED;
            $palletProduct->registered_at = date("Y-m-d H:i:s");
            $palletProduct->registered_user_id = $user->id;
            $palletProduct->quantity = $product->quantity;
            $palletProduct->amount = $product->amount;
            $palletProduct->repair_symptom_id = $symptomId;
            $palletProduct->repair_process_id = $processId;
            $palletProduct->repair_grade_id = $gradeId;
            $palletProduct->invoice1 = $invoice1;
            $palletProduct->invoice2 = $invoice2;
            $palletProduct->invoice3 = $invoice3;
            $palletProduct->memo = $data['memo'] ?? '';

            $palletProduct->save();

            // 상품 상태 업데이트
            $product->status = Product::STATUS_CHECKED_ON_PALLET;
            $product->save();

            // 상태 변환/통계::상품 상태점검완료(적재중)
            // 상태 변환/통계::팔레트 상품(적재중)
            $workStatusService = new WorkStatusService();
            $statusIds = $workStatusService->getStatusIds([
                'symptom_code' => $data['symptom_code'],
                'process_code' => $data['process_code'],
                'grade_code' => $data['grade_code'],
                'os_reinstall' => $data['os_reinstall'] ?? null,
                'add_fee' => $data['add_fee'] ?? null,
                'add_parts' => $data['add_parts'] ?? null,
            ]);
            $now = date('Y-m-d H:i:s');

            // 공통으로 사용될 기본 로그 데이터
            $baseLogData = [
                'product_id' => $product->id,
                'user_id' => $user->id,
                'memo' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $productLogs = [];

//            // 1. 추가 상태에 대한 로그들 (XL 등급, 수리 관련 상태 등)
//            foreach ($statusIds as $linkCode => $statusId) {
//                // 기본 상태들은 나중에 입력해야 해서 건너 뜀
//                if (in_array($linkCode, [
//                    WorkStatus::LINK_REPAIR_COMPLETE,
//                    WorkStatus::LINK_PALLET_PRODUCT_INSPECT,
//                    WorkStatus::LINK_PALLET_PRODUCT_REGISTERED
//                ])) {
//                    continue;
//                }
//
//                $productLogs[] = array_merge($baseLogData, [
//                    'model_type' => 'App\Models\Product',
//                    'model_id' => $product->id,
//                    'work_status_id' => $statusId,
//                ]);
//            }
//
//            // 2. 상품 점검 완료 로그
//            if (isset($statusIds[WorkStatus::LINK_REPAIR_COMPLETE])) {
//                $productLogs[] = array_merge($baseLogData, [
//                    'model_type' => 'App\Models\Product',
//                    'model_id' => $product->id,
//                    'work_status_id' => $statusIds[WorkStatus::LINK_REPAIR_COMPLETE],
//                ]);
//            }

            // 3. 상품 적재(미검수) 로그
            if (isset($statusIds[WorkStatus::LINK_PALLET_PRODUCT_INSPECT])) {
                $productLogs[] = array_merge($baseLogData, [
                    'model_type' => 'App\Models\PalletProduct',
                    'model_id' => $palletProduct->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_PALLET_PRODUCT_INSPECT],
                ]);
            }

            // 4. 상품 적재중 로그
            if (isset($statusIds[WorkStatus::LINK_PALLET_PRODUCT_REGISTERED])) {
                $productLogs[] = array_merge($baseLogData, [
                    'model_type' => 'App\Models\PalletProduct',
                    'model_id' => $palletProduct->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_PALLET_PRODUCT_REGISTERED],
                ]);
            }

            // 모든 로그 데이터 한 번에 삽입
            if (!empty($productLogs)) {
                ProductLog::insert($productLogs);
            }

            /**
             * 점검완료(적재중) 카운팅<br>
             * 수리/점검완료(창고) : repaired - 1, 점검완료(적재중) : checked_out + 1
             */
            // 카운터 기록
            $countService = new CountService();
            $counters[$product->req_id] = [
                'repaired' => -1,
                'checkout' => 1
            ];
            $countService->multipleUpdate($counters);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            HelperLibrary::slackErrorLog('pallet', '쿠팡RP상품 수리/점검완료 처리 실패', $e);

            throw $e;
        }
    }
}
