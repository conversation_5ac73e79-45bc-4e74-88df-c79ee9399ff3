<?php

namespace App\Http\Requests;

use App\Models\RepairProduct;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class StorePalletProductRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'location_place' => 'required|string|max:255',
            'location_code' => 'required|string|max:255',
            'product_id' => 'required|integer',
            'qaid' => 'required|string',
            'repair_product_id' => 'required|integer',
            'memo' => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            'location_place.required' => '팔레트가 선택 되지 않았습니다.',
            'location_code.required' => '팔레트를 선택해 주세요.',
            'product_id.required' => '등록할 상품 정보가 없습니다.',
            'qaid.required' => '등록할 상품 정보가 없습니다. QAID를 입력해 주세요.',
            'repair_product_id.required' => '수리된 상품을 선택해 주세요.',
        ];
    }

    public function after(Validator $validator): void
    {
        // after() 메서드에서는 $this->validated() 대신 $this->input()을 사용
        $repairProductId = $this->input('repair_product_id');

        if (!$repairProductId) {
            return; // repair_product_id가 없으면 기본 validation에서 처리됨
        }

        $repairProduct = RepairProduct::where('id', $repairProductId)
            ->where('status', RepairProduct::STATUS_REPAIRED)->first();

        if ($repairProduct === null) {
            $validator->errors()->add('repair_product_id', '저장하려는 상품이 존재 하지 않습니다.');
            return;
        }

        if ($repairProduct->repairGrade->code !== 'XL' && $repairProduct->invoice1 === 0) {
            $gradeName = $repairProduct->repairGrade->name;
            $validator->errors()->add('repair_product_id', "[$gradeName]의 청구 비용이 0원이 되어서는 안 됩니다.");
        }
    }
}
